# BRILLAR - Premium Entertainment Venue Website

A sophisticated and luxurious website for BRILLAR, an upscale entertainment venue featuring multiple locations across Tokyo. Built with modern React and TypeScript, this project showcases elegant design, smooth animations, and comprehensive venue information with bilingual support.

## 🌟 Features

### Core Functionality
- **Bilingual Support**: Complete Japanese and English language implementation
- **Responsive Design**: Fully optimized for mobile, tablet, and desktop experiences
- **Smooth Animations**: Performance-optimized transitions and scroll-triggered effects
- **Luxurious UI**: Elegant gold and black color scheme with premium aesthetics

### Key Sections
- **Hero Section**: Eye-catching introduction with dynamic background slideshow
- **About**: Detailed venue information and brand philosophy
- **Events & Memories**: Featured events with interactive galleries
- **Cast Profiles**: Professional performer profiles with routing
- **Shop Overview**: Multi-location carousel with detailed information
- **Gallery**: High-quality photography showcase
- **News**: Latest updates and announcements
- **Contact**: Comprehensive contact information and social media links

### Technical Highlights
- **Performance Optimized**: Lazy loading, optimized images, and efficient rendering
- **SEO Ready**: Proper meta tags, semantic HTML, and structured data
- **Accessibility**: WCAG compliant with proper contrast ratios and keyboard navigation
- **Mobile First**: Progressive enhancement from mobile to desktop

## 🛠 Technology Stack

- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and optimized builds
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: Radix UI primitives with shadcn/ui components
- **Icons**: Lucide React icon library
- **Routing**: React Router v6
- **Animations**: Framer Motion and GSAP
- **State Management**: React hooks and context
- **Package Manager**: Bun for fast dependency management

## 🚀 Getting Started

### Prerequisites
- Node.js (v18 or higher)
- Bun package manager (recommended) or npm/yarn

### Installation

```bash
# Clone the repository
git clone <your-repository-url>
cd BRILLAR-entertainment

# Install dependencies
bun install

# Start development server
bun run dev

# Build for production
bun run build

# Preview production build
bun run preview
```

## 📁 Project Structure

```
src/
├── components/          # React components
│   ├── ui/             # Reusable UI components (shadcn/ui)
│   ├── Hero.tsx        # Landing hero section
│   ├── ShopOverview.tsx # Store locations carousel
│   ├── Gallery.tsx     # Photo gallery component
│   ├── EventsMemories.tsx # Events showcase
│   └── ...             # Other feature components
├── pages/              # Route components
│   ├── Index.tsx       # Home page
│   ├── AllCast.tsx     # Cast listing
│   ├── CastProfile.tsx # Individual cast pages
│   └── StoreDetail.tsx # Store detail pages
├── hooks/              # Custom React hooks
├── data/               # Static data and content
├── images/             # Image assets
├── styles/             # Global styles and animations
└── lib/                # Utility functions
```

## 🎨 Design Philosophy

BRILLAR represents luxury entertainment at its finest. The design reflects this through:

- **Sophisticated Color Palette**: Rich blacks, elegant golds, and subtle gradients
- **Premium Typography**: Carefully selected serif and sans-serif font combinations  
- **High-Quality Imagery**: Professional photography and curated visuals
- **Intuitive UX**: Seamless navigation with delightful micro-interactions
- **Cultural Awareness**: Thoughtful bilingual implementation for Japanese and English audiences

## 🌐 Development Approach

This project was built with attention to:

1. **Clean Code Architecture**: Well-organized components with clear separation of concerns
2. **Performance**: Optimized rendering and minimal bundle size
3. **Maintainability**: TypeScript for type safety and clear component interfaces
4. **User Experience**: Smooth animations and intuitive navigation
5. **Scalability**: Modular design for easy feature additions

## 📱 Browser Support

- Chrome (latest)
- Firefox (latest) 
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🔧 Development Notes

- Uses modern React patterns including hooks and functional components
- Implements custom hooks for intersection observer, language switching, and mobile detection
- Tailwind CSS with custom configuration for the luxury aesthetic
- Optimized images with lazy loading and proper aspect ratios
- Careful attention to accessibility and semantic HTML

## 📄 License

This project is proprietary. All rights reserved.

---

**BRILLAR** - Where luxury meets entertainment
