
import React, { useRef, useEffect } from "react";
import { motion } from "framer-motion";
import { useLanguage } from "@/hooks/use-language";
import { useSmoothIntersection } from "@/hooks/use-intersection-observer";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Card, CardContent } from "@/components/ui/card";
import host1 from '../images/hero1.webp';
import host2 from '../images/hero2.webp';
import host3 from '../images/hero3.webp';
import host4 from '../images/hero4.webp';

const About: React.FC = () => {
  const { t } = useLanguage();
  const ref = useRef<HTMLDivElement>(null);
  const { isIntersecting: isInView, hasTriggered } = useSmoothIntersection(ref, { threshold: 0.05 });
  
  const [api, setApi] = React.useState<any>();
  const [current, setCurrent] = React.useState(0);
  const [count, setCount] = React.useState(0);
  const [isHovered, setIsHovered] = React.useState(false);

  // Reliable autoplay and loop functionality
  React.useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });

    // Custom autoplay implementation
    const interval = setInterval(() => {
      if (!isHovered && api) {
        if (api.canScrollNext()) {
          api.scrollNext();
        } else {
          // Manually scroll to first slide when at the end
          api.scrollTo(0);
        }
      }
    }, 4000);

    return () => clearInterval(interval);
  }, [api, isHovered]);

  // Ensure autoplay starts when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      if (api) {
        api.reInit();
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [api]);

  const topHostesses = [
    {
      id: 1,
      name: "カレン",
      nickname: "Champagne Queen 🍾",
      image: host1,
      quote: "私と過ごす夜は、きっと忘れられない特別な時間になります✨"
    },
    {
      id: 2,
      name: "サラ",
      nickname: "Diamond Princess 💎",
      image: host2,
      quote: "あなたの心を癒し、最高の笑顔をお届けします💎"
    },
    {
      id: 3,
      name: "マヤ",
      nickname: "Golden Goddess 🌟",
      image: host3,
      quote: "贅沢と美しさで、あなたの夢を現実にします🌟"
    },
    {
      id: 4,
      name: "ユナ",
      nickname: "Night Angel 🌙",
      image: host4,
      quote: "優雅さと情熱で、特別なひとときをお約束します🌙"
    },
  ];

  const clubStats = [
    {
      id: 1,
      icon: "🏆",
      title: "Top-Rated in Roppongi",
      subtitle: "5-Star Excellence"
    },
    {
      id: 2,
      icon: "💎",
      title: "4 Exclusive Locations",
      subtitle: "Prime Districts"
    },
    {
      id: 3,
      icon: "🍾",
      title: "10,000+ Champagne Calls",
      subtitle: "Last Year Alone"
    },
    {
      id: 4,
      icon: "🌟",
      title: "100+ Elite Hostesses",
      subtitle: "Handpicked Beauty"
    }
  ];
  
  return (
    <section id="about" className="py-5 px-4 md:px-8 relative overflow-hidden pt-8 bg-[#0d0c0a]" style={{background:'#0d0c0a'}}>
      {/* Luxury background elements */}
      {/* <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/70 to-black" />
      <div className="absolute top-0 left-0 w-full h-full opacity-10"
           style={{
             backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Cpath d='M30 30l15-15v30l-15-15zm-15 0l-15-15v30l15-15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
           }} /> */}
      
      {/* Floating particles */}
      {/* <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-gold/30 rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          />
        ))}
      </div> */}
      
      <div 
        ref={ref}
        className={`max-w-7xl mx-auto reveal relative z-10 ${isInView ? 'active' : ''}`}
      >
        {/* Main Title */}
        <div className="text-center">
          <h2 className="text-2xl md:text-6xl font-serif font-bold mb-6 text-gold gold-glow">
            ようこそ、非日常の楽園へ。洗練と癒しが織りなす、極上のひとときを貴方に。
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto mb-8"></div>
          
        </div>

       
        
      </div>
    </section>
  );
};

export default About;
