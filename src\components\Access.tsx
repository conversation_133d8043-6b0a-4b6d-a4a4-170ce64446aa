
import React, { useRef } from "react";
import { useLanguage } from "@/hooks/use-language";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";

const Access: React.FC = () => {
  const { t } = useLanguage();
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useIntersectionObserver(ref, { threshold: 0.1 });
  
  return (
    <section id="access" className="py-24 px-4 md:px-8">
      <div 
        ref={ref}
        className={`max-w-6xl mx-auto reveal ${isInView ? 'active' : ''}`}
      >
        <h2 className="section-heading">{t('access')}</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Map */}
          <div className="w-full h-[300px] md:h-[400px] bg-secondary">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3241.3776177770373!2d139.76493811744386!3d35.6799331!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x60188bfbd89f700b%3A0x277c49ba34ed38!2z5p2x5Lqs6aeF!5e0!3m2!1sja!2sjp!4v1621839182452!5m2!1sja!2sjp"
              width="100%"
              height="100%"
              style={{ border: 0 }}
              allowFullScreen
              loading="lazy"
              title="Google Map"
            />
          </div>
          
          {/* Info */}
          <div className="bg-secondary bg-opacity-70 p-6 md:p-8">
            <div className="space-y-6">
              <div>
                <div className="flex items-start gap-4">
                  <i className="fas fa-map-marker-alt text-gold mt-1"></i>
                  <div>
                    <h3 className="text-lg font-medium text-gold mb-2">{t('address')}</h3>
                    <p className="text-gold-light">{t('addressValue')}</p>
                  </div>
                </div>
              </div>
              
              <div>
                <div className="flex items-start gap-4">
                  <i className="fas fa-phone text-gold mt-1"></i>
                  <div>
                    <h3 className="text-lg font-medium text-gold mb-2">{t('phone')}</h3>
                    <p className="text-gold-light">{t('phoneValue')}</p>
                  </div>
                </div>
              </div>
              
              <div>
                <div className="flex items-start gap-4">
                  <i className="fas fa-clock text-gold mt-1"></i>
                  <div>
                    <h3 className="text-lg font-medium text-gold mb-2">{t('hours')}</h3>
                    <p className="text-gold-light">{t('hoursValue')}</p>
                    <p className="text-gold-light mt-1">{t('closed')}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Access;
