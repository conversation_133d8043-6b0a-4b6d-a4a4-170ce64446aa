
import React, { useEffect, useState, useCallback, useMemo, useRef } from "react";
import { cn } from "@/lib/utils";

interface BackgroundSlideshowProps {
  images: string[];
  interval?: number;
  className?: string;
  enableAnimations?: boolean;
}

const BackgroundSlideshow: React.FC<BackgroundSlideshowProps> = ({
  images,
  interval = 5000,
  className,
  enableAnimations = true
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [loadedImages, setLoadedImages] = useState<Set<number>>(new Set([0]));
  const [isMobile, setIsMobile] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const preloadedImages = useRef<HTMLImageElement[]>([]);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Preload images for better performance
  const preloadImages = useCallback(() => {
    images.forEach((src, index) => {
      if (!preloadedImages.current[index]) {
        const img = new Image();
        img.onload = () => {
          setLoadedImages(prev => new Set([...prev, index]));
        };
        img.src = src;
        preloadedImages.current[index] = img;
      }
    });
  }, [images]);

  useEffect(() => {
    preloadImages();
  }, [preloadImages]);

  // Optimized slideshow logic with mobile considerations
  const startSlideshow = useCallback(() => {
    if (images.length <= 1 || (isMobile && !enableAnimations)) return;

    intervalRef.current = setInterval(() => {
      setCurrentImageIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % images.length;
        // Preload next image if not already loaded
        if (!loadedImages.has(nextIndex)) {
          const img = new Image();
          img.onload = () => setLoadedImages(prev => new Set([...prev, nextIndex]));
          img.src = images[nextIndex];
        }
        return nextIndex;
      });
    }, isMobile ? interval * 1.5 : interval); // Slower on mobile
  }, [images, interval, isMobile, enableAnimations, loadedImages]);

  useEffect(() => {
    startSlideshow();
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [startSlideshow]);

  // Memoized styles for performance
  const containerStyles = useMemo(() => ({
    willChange: isMobile ? 'auto' : 'opacity',
    backfaceVisibility: 'hidden' as const,
    perspective: '1000px'
  }), [isMobile]);

  if (images.length === 0) return null;

  return (
    <div
      className={cn("absolute inset-0 overflow-hidden", className)}
      style={containerStyles}
    >
      {images.map((image, index) => {
        const isActive = index === currentImageIndex;
        const isLoaded = loadedImages.has(index);

        return (
          <div
            key={index}
            className={cn(
              "absolute inset-0 bg-cover bg-center",
              // Mobile optimizations: reduce animations
              isMobile
                ? "transition-opacity duration-500"
                : "transition-opacity duration-1000",
              // Only apply zoom animation on desktop and if enabled
              !isMobile && enableAnimations && "animate-zoom-slow",
              isActive ? "opacity-100 z-10" : "opacity-0 z-0"
            )}
            style={{
              backgroundImage: isLoaded ? `url(${image})` : 'none',
              transform: isMobile ? 'translateZ(0)' : undefined, // GPU acceleration hint
              willChange: isActive && !isMobile ? 'transform' : 'auto'
            }}
          />
        );
      })}
      <div className="absolute inset-0 bg-hero-gradient z-20" />
    </div>
  );
};

export default BackgroundSlideshow;
