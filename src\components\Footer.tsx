
import React, { useRef } from "react";
import { useLanguage } from "@/hooks/use-language";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";
import { Instagram, Twitter, Youtube, MapPin, Phone, Clock } from "lucide-react";
import logo from "../images/logo.webp"; // Adjust the path as necessary
const Footer: React.FC = () => {
  const { t, language } = useLanguage();
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useIntersectionObserver(ref, { threshold: 0.02 });
  
  // Updated navigation links for correct navigation/scrolling
  const navigationLinks = [
    { label: "Home", labelJa: "ホーム", section: "home" },
    { label: "Cast", labelJa: "キャスト", href: "/cast" },
    { label: "Career", labelJa: "キャリア", section: "recruitment" },
    { label: "Notice", labelJa: "お知らせ", section: "notice" },
  ];

  const socialLinks = [
    { icon: Instagram, href: "https://www.instagram.com/brillar_2022/?hl=en", label: "Instagram" },
    { 
      icon: () => (
        <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M24 10.304c0-5.369-5.383-9.738-12-9.738S0 4.935 0 10.304c0 4.839 4.327 8.874 10.199 9.579.373.08.881.244 1.008.565.115.292.076.764.037 1.061l-.164 1.02c-.048.287-.227 1.117.977.608 1.204-.509 6.497-3.826 8.862-6.55C23.176 14.647 24 12.647 24 10.304z"/>
        </svg>
      ), 
      href: "https://lin.ee/A7EOdNV", 
      label: "LINE" 
    },
    { 
      icon: () => (
        <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
        </svg>
      ), 
      href: "https://www.tiktok.com/@brillr2022?_t=ZS-8y9d5WVfRoO&_r=1", 
      label: "TikTok" 
    }
  ];

  const contactInfo = [
    { icon: MapPin, text: <>〒400-0032<br/>山梨県甲府市中央1-12-5五光ビル 4F</> },
    { icon: Phone, text: "************" },
    { icon: Clock, text: "21:00 - Last (日曜日は定休日です)" }
  ];
    return (
    <footer 
      ref={ref}
      className="relative py-20 px-4 md:px-8 bg-black overflow-hidden"
    >
      {/* Enhanced Background Pattern */}
      <div className="absolute inset-0">
        {/* Base gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
        
        {/* Geometric pattern overlay */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(212,175,55,0.1)_25%,rgba(212,175,55,0.1)_50%,transparent_50%,transparent_75%,rgba(212,175,55,0.1)_75%)] bg-[length:60px_60px]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(212,175,55,0.15)_0%,transparent_50%)]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(212,175,55,0.1)_0%,transparent_50%)]"></div>
        </div>

        {/* Subtle texture overlay */}
        <div className="absolute inset-0 opacity-5 bg-[repeating-linear-gradient(45deg,transparent,transparent_2px,rgba(255,255,255,0.03)_2px,rgba(255,255,255,0.03)_4px)] mix-blend-overlay"></div>
      </div>
      
      <div className="max-w-7xl mx-auto relative z-10">        {/* Header with Logo */}
        <div className="text-center mb-16">
          <div className="inline-block mb-6">
            <img 
              src={logo}
              alt="BRILLAR Logo" 
              className="h-16 md:h-20 mx-auto filter brightness-0 invert opacity-90 hover:opacity-100 transition-opacity duration-300"
            />
          </div>
          <h2 className="text-3xl md:text-4xl font-serif font-bold text-gold gold-glow mb-4">
            BRILLAR
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto"></div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12 mb-12">          {/* Navigation Links */}
          <div className="space-y-6">
            <h3 className="text-xl font-serif text-gold mb-4 border-b border-gold/30 pb-2">Navigation</h3>
            <nav className="space-y-3">
              {navigationLinks.map((link, index) => {
                if (link.href) {
                  // For direct links (e.g. Cast page)
                  return (
                    <a
                      key={link.label}
                      href={link.href}
                      className="group block text-gray-300 hover:text-gold transition-all duration-300 text-sm md:text-base"
                    >
                      <span className="relative">
                        {language === 'en' ? link.label : link.labelJa}
                        <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-gold to-gold-light group-hover:w-full transition-all duration-300"></div>
                      </span>
                    </a>
                  );
                } else if (link.section) {
                  // For scroll/section links
                  return (
                    <button
                      key={link.label}
                      className="group block text-left w-full text-gray-300 hover:text-gold transition-all duration-300 text-sm md:text-base bg-transparent border-0 p-0 m-0"
                      onClick={() => {
                        if (link.section === "home") {
                          window.location.href = "/";
                        } else {
                          const event = new CustomEvent("menu-section-scroll", { detail: { section: link.section } });
                          window.dispatchEvent(event);
                        }
                      }}
                    >
                      <span className="relative">
                        {language === 'en' ? link.label : link.labelJa}
                        <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-gold to-gold-light group-hover:w-full transition-all duration-300"></div>
                      </span>
                    </button>
                  );
                }
                return null;
              })}
            </nav>
          </div>          {/* Contact Information */}
          <div className="space-y-6">
            <h3 className="text-xl font-serif text-gold mb-4 border-b border-gold/30 pb-2">Contact</h3>
            <div className="space-y-4">
              {contactInfo.map((item, index) => (
                <div key={index} className="flex items-start space-x-3 text-gray-300">
                  <item.icon className="w-5 h-5 text-gold mt-0.5 flex-shrink-0" />
                  <span className="text-sm md:text-base">{item.text}</span>
                </div>
              ))}
            </div>
          </div>          {/* Social Media */}
          <div className="space-y-6">
            <h3 className="text-xl font-serif text-gold mb-4 border-b border-gold/30 pb-2">Follow Us</h3>
            <div className="space-y-4">
              {socialLinks.map((social, index) => {
                const IconComponent = social.icon;
                return (
                  <a
                    key={social.label}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group flex items-center space-x-3 text-gray-300 hover:text-gold transition-all duration-300"
                  >
                    <div className="p-2 bg-card/30 rounded-lg border border-gold/20 group-hover:border-gold/60 group-hover:bg-card/50 transition-all duration-300">
                      <IconComponent className="w-4 h-4" />
                    </div>
                    <span className="text-sm md:text-base">{social.label}</span>
                  </a>
                );
              })}
            </div>
          </div>
        </div>        {/* Bottom Section */}
        <div className="pt-8 border-t border-gold/20">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-gray-400 text-sm font-light tracking-wide">
              Copyright © BRILLAR GROUP All rights reserved.
            </p>
            <div className="flex items-center space-x-6">
              <a href="#privacy" className="text-gray-400 hover:text-gold text-sm transition-colors duration-300">
                Privacy Policy
              </a>
              <a href="#terms" className="text-gray-400 hover:text-gold text-sm transition-colors duration-300">
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Decorative corner elements - more subtle */}
      <div className="absolute top-8 left-8 w-12 h-12 border-l-2 border-t-2 border-gold/20 opacity-50"></div>
      <div className="absolute top-8 right-8 w-12 h-12 border-r-2 border-t-2 border-gold/20 opacity-50"></div>
      <div className="absolute bottom-8 left-8 w-12 h-12 border-l-2 border-b-2 border-gold/20 opacity-50"></div>
      <div className="absolute bottom-8 right-8 w-12 h-12 border-r-2 border-b-2 border-gold/20 opacity-50"></div>
    </footer>
  );
};

export default Footer;
