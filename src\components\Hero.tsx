import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useLanguage } from "@/hooks/use-language";
import hero1 from "../images/hero1.webp";
import hero2 from "../images/hero2.webp";
import hero3 from "../images/hero3.webp";
import hero4 from "../images/hero4.webp";
import mainhero1 from "../images/mainhero1.webp";
import mainhero2 from "../images/mainhero2.webp";

interface HeroProps {
  startSlider?: boolean;
}

const Hero: React.FC<HeroProps> = ({ startSlider = true }) => {
  const { t } = useLanguage();
  const [currentSlide, setCurrentSlide] = useState(0);
  
  const slides = [
    {
      id: 1,
      image: mainhero1,
      title: "BR<PERSON>LA<PERSON>",
      subtitle: "非日常へようこそ"
    },
    {
      id: 2,
      image: mainhero2,
      title: "BR<PERSON>LAR",
      subtitle: "今日という唯一無二の時を最高の笑顔で"
    },
    
  ];

  // Auto-play slider - only start when preloader is complete
  useEffect(() => {
    if (!startSlider) return;

    // Reset to first slide when slider starts to ensure users see the full first slide
    setCurrentSlide(0);

    let interval: NodeJS.Timeout;

    // Add a small delay to let the hero section fully appear before starting the slider
    const startDelay = setTimeout(() => {
      interval = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % slides.length);
      }, 5000); // Change slide every 5 seconds
    }, 1000); // 1 second delay to let users appreciate the first slide

    return () => {
      clearTimeout(startDelay);
      if (interval) clearInterval(interval);
    };
  }, [slides.length, startSlider]);
  
  return (
    <section id="hero" className="relative h-screen w-full overflow-hidden bg-black !p-0 !m-0">
      {/* Background Slider */}
      <div className="absolute inset-0">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentSlide}
            className="absolute inset-0 w-full h-full"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{
              duration: 0.8,
              ease: "easeInOut"
            }}
          >            {/* Background Image with Optimized Animation */}
            <motion.div
              className="absolute inset-0 w-full h-full bg-cover bg-center md:bg-cover sm:bg-contain"
              style={{
                backgroundImage: `url(${slides[currentSlide].image})`,
                backgroundPosition: 'center center',
                backgroundRepeat: 'no-repeat'
              }}
              initial={{ scale: 1.02 }}
              animate={{ scale: 1 }}
              transition={{ duration: 8, ease: "easeOut" }}
            />
            
            {/* Elegant Gradient Overlay */}
            <div className="absolute inset-0 bg-gradient-to-b from-black/40 via-black/20 to-black/70" />
            <div className="absolute inset-0 bg-gradient-to-r from-black/30 via-transparent to-black/30" />
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Luxury Pattern Overlay */}
      <div 
        className="absolute inset-0 opacity-10 z-10" 
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.15'%3E%3Cpath d='M50 50m-20 0a20,20 0 1,1 40,0a20,20 0 1,1 -40,0 M50 10m-5 0a5,5 0 1,1 10,0a5,5 0 1,1 -10,0 M50 90m-5 0a5,5 0 1,1 10,0a5,5 0 1,1 -10,0 M10 50m-5 0a5,5 0 1,1 10,0a5,5 0 1,1 -10,0 M90 50m-5 0a5,5 0 1,1 10,0a5,5 0 1,1 -10,0'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} 
      />

      {/* Hero Content */}
      <div className="relative z-20 flex flex-col items-center justify-center h-full px-4 text-center">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentSlide}
            className="max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -30 }}
            transition={{
              duration: 0.6,
              delay: 0.2,
              ease: "easeOut"
            }}
          >            {/* Main Title */}
            <motion.h1 
              className="font-serif text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-thin mb-6 tracking-wider relative"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              style={{
                background: 'linear-gradient(45deg, #D4AF37, #FFD700, #D4AF37)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                filter: 'drop-shadow(0 0 20px rgba(212, 175, 55, 0.5))'
              }}
            >
              {slides[currentSlide].title}
            </motion.h1>
            
            {/* Subtitle */}
            <motion.p 
              className="font-light text-xl md:text-2xl lg:text-3xl text-white/90 mb-12 tracking-wide"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.7 }}
            >
              {slides[currentSlide].subtitle}
            </motion.p>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30">
        <div className="flex space-x-3">
          {slides.map((_, index) => (            <motion.button
              key={index}
              className={`w-3 h-3 rounded-full border transition-all duration-300 ${
                currentSlide === index 
                  ? 'bg-[#D4AF37] border-[#D4AF37] shadow-lg shadow-[#D4AF37]/50' 
                  : 'bg-transparent border-white/50 hover:border-white/80'
              }`}
              onClick={() => setCurrentSlide(index)}
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.9 }}
              initial={{ opacity: 0 }}
              animate={{ opacity: startSlider ? 1 : 0 }}
              transition={{ duration: 0.3, delay: startSlider ? 0.5 + index * 0.1 : 0 }}
            />
          ))}
        </div>
      </div>

      {/* Elegant Corner Decorations */}
      <div className="absolute top-8 left-8 z-20 opacity-40">
        <motion.div 
          className="w-20 h-20 border border-[#D4AF37]/60"
          style={{ clipPath: 'polygon(0 0, 40% 0, 0 40%)' }}
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: startSlider ? 0.4 : 0, scale: startSlider ? 1 : 0 }}
          transition={{ duration: 1, delay: startSlider ? 0.8 : 0 }}
        />
      </div>
      <div className="absolute top-8 right-8 z-20 opacity-40">
        <motion.div 
          className="w-20 h-20 border border-[#D4AF37]/60"
          style={{ clipPath: 'polygon(60% 0, 100% 0, 100% 40%)' }}
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: startSlider ? 0.4 : 0, scale: startSlider ? 1 : 0 }}
          transition={{ duration: 1, delay: startSlider ? 1.0 : 0 }}
        />
      </div>
      <div className="absolute bottom-8 left-8 z-20 opacity-40">
        <motion.div 
          className="w-20 h-20 border border-[#D4AF37]/60"
          style={{ clipPath: 'polygon(0 60%, 40% 100%, 0 100%)' }}
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: startSlider ? 0.4 : 0, scale: startSlider ? 1 : 0 }}
          transition={{ duration: 1, delay: startSlider ? 1.2 : 0 }}
        />
      </div>
      <div className="absolute bottom-8 right-8 z-20 opacity-40">
        <motion.div 
          className="w-20 h-20 border border-[#D4AF37]/60"
          style={{ clipPath: 'polygon(60% 100%, 100% 60%, 100% 100%)' }}
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: startSlider ? 0.4 : 0, scale: startSlider ? 1 : 0 }}
          transition={{ duration: 1, delay: startSlider ? 1.4 : 0 }}
        />
      </div>
    </section>
  );
};

export default Hero;
