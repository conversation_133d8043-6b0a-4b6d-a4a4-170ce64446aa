
import React, { useRef } from "react";
import { useLanguage } from "@/hooks/use-language";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";

// Sample news data
const newsItems = [
  {
    id: 1,
    date: "2025.05.01",
    titleJa: "ゴールデンウィークイベント開催！",
    titleEn: "Golden Week Special Event!",
  },
  {
    id: 2,
    date: "2025.04.15",
    titleJa: "新人ホステス「ユキ」デビュー！",
    titleEn: "New Hostess 'Yuki' Debut!",
  },
  {
    id: 3,
    date: "2025.04.01",
    titleJa: "春の特別イベント 「桜祭り」",
    titleEn: "Spring Special Event 'Cherry Blossom Festival'",
  },
  {
    id: 4,
    date: "2025.03.20",
    titleJa: "営業時間変更のお知らせ",
    titleEn: "Notice of Business Hours Change",
  }
];

const News: React.FC = () => {
  const { t, language } = useLanguage();
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useIntersectionObserver(ref, { threshold: 0.1 });
  
  return (
    <section id="news" className="py-24 px-4 md:px-8">
      <div 
        ref={ref}
        className={`max-w-6xl mx-auto reveal ${isInView ? 'active' : ''}`}
      >
        <h2 className="section-heading">{t('news')}</h2>
        
        <div className="bg-secondary bg-opacity-70 p-6 md:p-8 rounded-sm">
          <ul className="space-y-4 md:space-y-6">
            {newsItems.map((item) => (
              <li key={item.id} className="border-b border-gold-dark border-opacity-30 pb-4 md:pb-6 last:border-0">
                <a href="#" className="flex flex-col md:flex-row md:items-center gap-2 md:gap-8 hover:text-gold-light transition-colors duration-200">
                  <time className="text-gold font-medium whitespace-nowrap">{item.date}</time>
                  <span>{language === 'en' ? item.titleEn : item.titleJa}</span>
                </a>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  );
};

export default News;
