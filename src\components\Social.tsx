
import React, { useRef } from "react";
import { useLanguage } from "@/hooks/use-language";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";

// Sample social posts data
const socialPosts = [
  {
    id: 1,
    type: "instagram",
    image: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80",
    captionJa: "本日のイベントの様子 #BRILLAR",
    captionEn: "Today's event highlights #BRILLAR"
  },
  {
    id: 2,
    type: "twitter",
    image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80",
    captionJa: "新メニュー追加のお知らせ #BRILLAR",
    captionEn: "Announcing our new menu items #BRILLAR"
  },
  {
    id: 3,
    type: "instagram",
    image: "https://images.unsplash.com/photo-1726374965328-7f61d4dc18c5?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80",
    captionJa: "週末のパーティー準備中 #BRILLAR",
    captionEn: "Getting ready for weekend party #BRILLAR"
  }
];

const Social: React.FC = () => {
  const { t, language } = useLanguage();
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useIntersectionObserver(ref, { threshold: 0.1 });
  
  return (
    <section id="sns" className="py-24 px-4 md:px-8 bg-secondary">
      <div 
        ref={ref}
        className={`max-w-6xl mx-auto reveal ${isInView ? 'active' : ''}`}
      >
        <h2 className="section-heading">SNS</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {socialPosts.map((post) => (
            <div key={post.id} className="bg-card rounded-sm overflow-hidden hover-scale">
              <div className="relative">
                <img 
                  src={post.image} 
                  alt="Social post"
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-4 right-4 text-xl">
                  {post.type === "instagram" ? (
                    <i className="fab fa-instagram text-gold"></i>
                  ) : (
                    <i className="fab fa-twitter text-gold"></i>
                  )}
                </div>
              </div>
              <div className="p-4">
                <p className="text-gold-light text-sm">
                  {language === 'en' ? post.captionEn : post.captionJa}
                </p>
              </div>
            </div>
          ))}
        </div>
        
        <div className="flex justify-center gap-8 mt-12">
          <a href="#" className="text-2xl text-gold hover:text-gold-light transition-colors duration-200">
            <i className="fab fa-instagram"></i>
          </a>
          <a href="#" className="text-2xl text-gold hover:text-gold-light transition-colors duration-200">
            <i className="fab fa-twitter"></i>
          </a>
          <a href="#" className="text-2xl text-gold hover:text-gold-light transition-colors duration-200">
            <i className="fab fa-line"></i>
          </a>
        </div>
      </div>
    </section>
  );
};

export default Social;
