
import React, { useRef, useEffect } from "react";
import { useLanguage } from "@/hooks/use-language";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";
import { Button } from "@/components/ui/button";
import { Instagram, Music, Twitter, Youtube } from "lucide-react";

// Sample Instagram posts data from hostess_club_ account
/* 
const instagramPosts = [
  {
    id: 1,
    image: "https://images.unsplash.com/photo-*************-2c8b550f87b3?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80",
    caption: "Elegant evening vibes ✨ #hostessclub"
  },
  {
    id: 2,
    image: "https://images.unsplash.com/photo-*************-2616c27b6c43?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80",
    caption: "Beautiful moments with our stunning cast 💫"
  },
  {
    id: 3,
    image: "https://images.unsplash.com/photo-*************-bcfd4ca60f91?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80",
    caption: "Luxury and sophistication in every detail 🍾"
  },
  {
    id: 4,
    image: "https://images.unsplash.com/photo-*************-53994a69daeb?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80",
    caption: "Unforgettable experiences await ✨"
  },
  {
    id: 5,
    image: "https://images.unsplash.com/photo-*************-4ff0802cfb7e?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80",
    caption: "Behind the scenes with our amazing team 💎"
  },
  {
    id: 6,
    image: "https://images.unsplash.com/photo-*************-b1c1722653e1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80",
    caption: "Premium service, exceptional moments 🌟"
  }
];
*/

const SocialMedia: React.FC = () => {
  const { language } = useLanguage();
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useIntersectionObserver(ref, { threshold: 0.02 });

  // No need for script loading when using iframe

  const socialAccounts = [
    {
      platform: "Instagram",
      handle: "@brillar_2022",
      icon: Instagram,
      url: "https://www.instagram.com/brillar_2022/?hl=en",
      color: "from-pink-500 to-purple-600",
      description: "Daily moments & exclusive content"
    },
    {
      platform: "TikTok",
      handle: "@brillr2022",
      icon: Music,
      url: "https://www.tiktok.com/@brillr2022?_t=ZS-8y9d5WVfRoO&_r=1",
      color: "from-black to-gray-800",
      description: "Behind the scenes videos"
    },
    {
      platform: "LINE",
      handle: "BRILLAR Official",
      icon: () => (
        <svg className="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
          <path d="M24 10.304c0-5.369-5.383-9.738-12-9.738S0 4.935 0 10.304c0 4.839 4.327 8.874 10.199 9.579.373.08.881.244 1.008.565.115.292.076.764.037 1.061l-.164 1.02c-.048.287-.227 1.117.977.608 1.204-.509 6.497-3.826 8.862-6.55C23.176 14.647 24 12.647 24 10.304z"/>
        </svg>
      ),
      url: "https://lin.ee/A7EOdNV",
      color: "from-green-400 to-green-600",
      description: "Direct communication"
    }
  ];

  return (
    <section className="py-24 px-4 md:px-8 bg-gradient-to-br from-black via-gray-900/50 to-black relative overflow-hidden">
      {/* Enhanced Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(212,175,55,0.1)_0%,transparent_70%)]"></div>
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_40%,rgba(212,175,55,0.03)_50%,transparent_60%)] bg-[length:40px_40px]"></div>
      </div>
      
      <div 
        ref={ref}
        className={`max-w-7xl mx-auto relative z-10 reveal ${isInView ? 'active' : ''}`}
      >
        {/* Section Header */}
        <div className="text-center mb-20" style={{ animationDelay: '0.2s' }}>
          <h2 className="section-heading mb-6">
            {language === 'en' ? 'Follow Our Moments' : '私たちの日常をチェック'}
          </h2>
          <p className="text-xl text-gold-light max-w-3xl mx-auto leading-relaxed">
            {language === 'en' 
              ? 'Stay connected with our daily luxury experiences and exclusive behind-the-scenes moments'
              : '私たちの日々の贅沢な体験と特別な舞台裏の瞬間をご覧ください'
            }
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto mt-8"></div>
        </div>

        {/* Instagram Posts Grid - Using Elfsight Widget */}
        <div className="mb-20" style={{ animationDelay: '0.4s' }}>
          <div className="text-center mb-12">
            <div className="inline-flex items-center space-x-3 text-gold mb-4">
              <Instagram className="w-8 h-8" />
              <h3 className="text-2xl font-serif font-semibold">@brillar_2022</h3>
            </div>
            <p className="text-gold-light">
              {language === 'en' ? 'Recent Posts' : '最新の投稿'}
            </p>
          </div>

          {/* SociableKit Instagram Reels Iframe */}
          <div className="max-w-6xl mx-auto">
            <iframe 
              src="https://widgets.sociablekit.com/instagram-reels/iframe/25583018"
              frameBorder="0"
              width="100%" 
              height="1000"
            ></iframe>
          </div>

          {/* Commented out hardcoded Instagram posts
          <div className="grid grid-cols-2 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
            {instagramPosts.map((post, index) => (
              <div 
                key={post.id}
                className="group relative bg-card/30 rounded-2xl overflow-hidden hover-scale border border-gold/20 hover:border-gold/40 transition-all duration-500"
                style={{ animationDelay: `${0.6 + index * 0.1}s` }}
              >
                <div className="aspect-square relative overflow-hidden">
                  <img 
                    src={post.image} 
                    alt="Instagram post"
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <div className="absolute bottom-4 left-4 right-4">
                      <p className="text-white text-sm line-clamp-2 font-medium">{post.caption}</p>
                    </div>
                  </div>
                  <div className="absolute top-4 right-4 bg-white/20 backdrop-blur-sm rounded-full p-2">
                    <Instagram className="w-4 h-4 text-white" />
                  </div>
                </div>
              </div>
            ))}
          </div>
          */}
        </div>

        {/* Social Media Handles */}
        <div className="max-w-4xl mx-auto" style={{ animationDelay: '0.8s' }}>
          <div className="text-center mb-12">
            <h3 className="text-3xl font-serif text-gold mb-4">
              {language === 'en' ? 'Connect With Us' : 'つながろう'}
            </h3>
            <p className="text-gold-light text-lg">
              {language === 'en' ? 'Follow us across all platforms' : 'すべてのプラットフォームでフォローしてください'}
            </p>
          </div>

          {/* Social Media Icons Row */}
          <div className="flex justify-center items-center space-x-4 sm:space-x-8 mb-12">
            {socialAccounts.map((account, index) => {
              const IconComponent = account.icon;
              return (
                <a
                  key={account.platform}
                  href={account.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group relative"
                  style={{ animationDelay: `${1.0 + index * 0.1}s` }}
                  title={`${account.platform} - ${account.handle}`}
                >
                  <div className="relative w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-gold/20 to-gold/5 backdrop-blur-sm rounded-full border border-gold/30 hover:border-gold/60 transition-all duration-500 hover:shadow-lg hover:shadow-gold/20 hover:scale-110 flex items-center justify-center group">
                    <IconComponent className="w-5 h-5 sm:w-7 sm:h-7 text-gold group-hover:text-white transition-colors duration-300" />
                    
                    {/* Glow effect on hover */}
                    <div className="absolute inset-0 bg-gradient-to-br from-gold/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full"></div>
                    
                    {/* Tooltip */}
                    <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 bg-black/90 backdrop-blur-sm text-gold text-xs px-3 py-1 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 whitespace-nowrap border border-gold/20">
                      {account.handle}
                    </div>
                  </div>
                </a>
              );
            })}
          </div>

          {/* Decorative separator */}
          <div className="flex justify-center items-center space-x-6 text-gold-light" style={{ animationDelay: '1.4s' }}>
            <div className="w-16 h-px bg-gradient-to-r from-transparent to-gold"></div>
            <span className="text-sm font-medium">
              {language === 'en' ? 'Join our community' : 'コミュニティに参加'}
            </span>
            <div className="w-16 h-px bg-gradient-to-l from-transparent to-gold"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SocialMedia;
