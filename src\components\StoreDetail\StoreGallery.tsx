import React, { useState, useRef, useEffect } from "react";
import { useLanguage } from "@/hooks/use-language";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";
import { ChevronLeft, ChevronRight, Maximize2, X, Play, Pause, Star, Heart } from "lucide-react";
import { Store } from "@/data/storeData";

interface StoreGalleryProps {
  store: Store;
}

const StoreGallery: React.FC<StoreGalleryProps> = ({ store }) => {
  const { language } = useLanguage();
  const [selectedImage, setSelectedImage] = useState<number | null>(null);
  const [isSlideshow, setIsSlideshow] = useState(false);
  const [likedImages, setLikedImages] = useState<Set<number>>(new Set());
  const [hoveredImage, setHoveredImage] = useState<number | null>(null);
  const galleryRef = useRef<HTMLDivElement>(null);
  const isGalleryInView = useIntersectionObserver(galleryRef, { threshold: 0.1 });
  const slideshowInterval = useRef<NodeJS.Timeout | null>(null);

  // Enhanced image titles for more luxurious feel
  const imageLabels = [
    { en: "Luxurious Main Lounge", jp: "豪華なメインラウンジ" },
    { en: "VIP Private Booth", jp: "VIPプライベートブース" },
    { en: "Premium Bar Counter", jp: "プレミアムバーカウンター" },
    { en: "Elegant Interior Design", jp: "エレガントなインテリア" },
    { en: "Exclusive Diamond Suite", jp: "限定ダイヤモンドスイート" },
    { en: "Crystal Chandelier Hall", jp: "クリスタルシャンデリアホール" },
    { en: "Golden Ambiance", jp: "ゴールデンアンビエンス" },
    { en: "Royal Entertainment Area", jp: "ロイヤルエンターテイメントエリア" }
  ];

  useEffect(() => {
    if (isSlideshow && selectedImage !== null) {
      slideshowInterval.current = setInterval(() => {
        setSelectedImage(prev => prev !== null ? (prev + 1) % store.images.length : 0);
      }, 3000);
    } else {
      if (slideshowInterval.current) {
        clearInterval(slideshowInterval.current);
        slideshowInterval.current = null;
      }
    }

    return () => {
      if (slideshowInterval.current) {
        clearInterval(slideshowInterval.current);
      }
    };
  }, [isSlideshow, selectedImage, store.images.length]);

  const toggleLike = (index: number, e: React.MouseEvent) => {
    e.stopPropagation();
    setLikedImages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  const openLightbox = (index: number) => {
    setSelectedImage(index);
    setIsSlideshow(false);
    document.body.style.overflow = 'hidden';
  };

  const closeLightbox = () => {
    setSelectedImage(null);
    setIsSlideshow(false);
    document.body.style.overflow = 'unset';
  };

  const toggleSlideshow = () => {
    setIsSlideshow(!isSlideshow);
  };

  const nextLightboxImage = () => {
    if (selectedImage !== null) {
      setSelectedImage((selectedImage + 1) % store.images.length);
    }
  };

  const prevLightboxImage = () => {
    if (selectedImage !== null) {
      setSelectedImage((selectedImage - 1 + store.images.length) % store.images.length);
    }
  };

  return (
    <>
      <section 
        ref={galleryRef}
        className={`py-24 md:py-32 px-4 md:px-8 bg-black reveal ${isGalleryInView ? 'active' : ''}`}
      >
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 md:mb-20">
            <h2 className="text-4xl md:text-5xl font-serif premium-gradient-text mb-6">
              {language === 'en' ? 'Gallery' : 'ギャラリー'}
            </h2>
            <div className="w-24 md:w-32 h-1 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto mb-6"></div>
            <p className="text-gold-light text-lg md:text-xl max-w-2xl mx-auto leading-relaxed">
              {language === 'en' 
                ? 'Experience the luxurious atmosphere and elegant interior design of our establishment'
                : '当店の豪華な雰囲気とエレガントなインテリアデザインをご体験ください'
              }
            </p>
          </div>

          {/* Enhanced Gallery Grid Layout */}
          <div className="relative">
            {/* Floating Filter Buttons */}
            

            {/* Masonry Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 auto-rows-max">
              {store.images.map((image, index) => {
                const label = imageLabels[index % imageLabels.length];
                const isLiked = likedImages.has(index);
                const isHovered = hoveredImage === index;
                
                return (
                  <div
                    key={index}
                    className={`gallery-card group relative cursor-pointer transform transition-all duration-700 hover:scale-105 ${
                      index % 5 === 0 ? 'row-span-2' : 
                      index % 3 === 0 ? 'row-span-1' : 'row-span-1'
                    }`}
                    style={{
                      height: index % 5 === 0 ? '400px' : index % 3 === 0 ? '280px' : '320px'
                    }}
                    onClick={() => openLightbox(index)}
                    onMouseEnter={() => setHoveredImage(index)}
                    onMouseLeave={() => setHoveredImage(null)}
                  >
                    {/* Diamond Sparkles */}
                    <div className="diamond-sparkle"></div>
                    <div className="diamond-sparkle"></div>
                    <div className="diamond-sparkle"></div>
                    <div className="diamond-sparkle"></div>

                    {/* Enhanced Background with Texture */}
                    <div className="absolute inset-0 bg-gradient-to-br from-gold/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    
                    <div className="relative h-full luxury-border">
                      <img
                        src={image}
                        alt={`${store.name} ${label?.[language]} || 'interior'}`}
                        className="gallery-image w-full h-full object-cover"
                      />
                      
                      {/* Premium Gradient Overlays */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      <div className="absolute inset-0 bg-gradient-to-r from-gold/10 via-transparent to-gold/10 opacity-0 group-hover:opacity-50 transition-opacity duration-700"></div>
                      
                      {/* Floating Action Buttons */}
                      <div className="absolute top-4 right-4 flex gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                        <button
                          onClick={(e) => toggleLike(index, e)}
                          className={`bg-black/50 backdrop-blur-sm p-2 rounded-full border border-gold/30 hover:scale-110 transition-all duration-300 ${
                            isLiked ? 'text-red-400 bg-red-500/20' : 'text-gold hover:text-red-400'
                          }`}
                        >
                          <Heart className={`w-4 h-4 ${isLiked ? 'fill-current' : ''}`} />
                        </button>
                        <button className="bg-black/50 backdrop-blur-sm text-gold p-2 rounded-full border border-gold/30 hover:scale-110 transition-all duration-300">
                          <Maximize2 className="w-4 h-4" />
                        </button>
                      </div>

                      {/* Image Number Badge */}
                      <div className="absolute top-4 left-4 bg-black/60 backdrop-blur-sm text-gold px-3 py-1 rounded-full border border-gold/30 opacity-0 group-hover:opacity-100 transition-all duration-300">
                        <span className="text-sm font-medium flex items-center gap-1">
                          <Star className="w-3 h-3" />
                          {index + 1}
                        </span>
                      </div>

                      {/* Enhanced Bottom Info with Animation */}
                      <div className="absolute bottom-0 left-0 right-0 p-6">
                        <div className={`transform transition-all duration-500 ${
                          isHovered ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
                        }`}>
                          <div className="bg-black/70 backdrop-blur-lg rounded-2xl p-4 border border-gold/30 shadow-xl">
                            <h4 className="text-gold font-serif text-lg mb-2 tracking-wide">
                              {label?.[language] || (language === 'en' ? 'Luxury Experience' : '贅沢な体験')}
                            </h4>
                            <p className="text-gold-light text-sm leading-relaxed">
                              {language === 'en' 
                                ? 'Immerse yourself in our premium atmosphere' 
                                : 'プレミアムな雰囲気に浸ってください'
                              }
                            </p>
                            <div className="flex items-center gap-2 mt-3">
                              <div className="flex gap-1">
                                {[...Array(5)].map((_, i) => (
                                  <Star key={i} className="w-3 h-3 text-gold fill-current" />
                                ))}
                              </div>
                              <span className="text-xs text-gold-light ml-2">Premium Quality</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          
        </div>
      </section>

      {/* Enhanced Lightbox Modal */}
      {selectedImage !== null && (
        <div className="lightbox-backdrop fixed inset-0 z-50 bg-black/97 backdrop-blur-md flex items-center justify-center p-4">
          <div className="relative max-w-7xl max-h-screen">
            {/* Enhanced Image Container */}
            <div className="relative">
              <img
                src={store.images[selectedImage]}
                alt={`${store.name} ${imageLabels[selectedImage % imageLabels.length]?.[language]}`}
                className="lightbox-image max-w-full max-h-screen object-contain rounded-2xl shadow-[0_0_100px_rgba(212,175,55,0.3)]"
              />
              
              {/* Image Info Overlay */}
              <div className="absolute bottom-4 left-4 right-4">
                <div className="bg-black/80 backdrop-blur-lg rounded-2xl p-6 border border-gold/30">
                  <h3 className="text-2xl font-serif text-gold mb-2">
                    {imageLabels[selectedImage % imageLabels.length]?.[language] || store.name}
                  </h3>
                  <p className="text-gold-light">
                    {language === 'en' 
                      ? 'Experience the pinnacle of luxury and elegance' 
                      : '贅沢とエレガンスの頂点を体験してください'
                    }
                  </p>
                  <div className="flex items-center gap-4 mt-4">
                    <div className="flex gap-1">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 text-gold fill-current" />
                      ))}
                    </div>
                    <button
                      onClick={(e) => toggleLike(selectedImage, e)}
                      className={`flex items-center gap-2 px-4 py-2 rounded-full border transition-all duration-300 ${
                        likedImages.has(selectedImage)
                          ? 'bg-red-500/20 border-red-400 text-red-400'
                          : 'bg-gold/10 border-gold/30 text-gold hover:bg-gold/20'
                      }`}
                    >
                      <Heart className={`w-4 h-4 ${likedImages.has(selectedImage) ? 'fill-current' : ''}`} />
                      <span className="text-sm">
                        {language === 'en' ? 'Like' : 'いいね'}
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Enhanced Close Button */}
            <button
              onClick={closeLightbox}
              className="absolute top-6 right-6 bg-black/70 backdrop-blur-sm text-gold hover:text-gold-light hover:bg-black/90 p-4 rounded-full transition-all duration-300 hover:scale-110 border border-gold/30 hover:border-gold/50 shadow-lg"
            >
              <X className="w-6 h-6" />
            </button>

            {/* Enhanced Navigation with Slideshow Controls */}
            {store.images.length > 1 && (
              <>
                <button
                  onClick={prevLightboxImage}
                  className="absolute left-6 top-1/2 -translate-y-1/2 bg-black/70 backdrop-blur-sm text-gold hover:text-gold-light hover:bg-black/90 p-4 rounded-full transition-all duration-300 hover:scale-110 border border-gold/30 hover:border-gold/50 shadow-lg"
                >
                  <ChevronLeft className="w-8 h-8" />
                </button>
                <button
                  onClick={nextLightboxImage}
                  className="absolute right-6 top-1/2 -translate-y-1/2 bg-black/70 backdrop-blur-sm text-gold hover:text-gold-light hover:bg-black/90 p-4 rounded-full transition-all duration-300 hover:scale-110 border border-gold/30 hover:border-gold/50 shadow-lg"
                >
                  <ChevronRight className="w-8 h-8" />
                </button>

                {/* Slideshow Toggle */}
                <button
                  onClick={toggleSlideshow}
                  className="absolute top-6 left-6 bg-black/70 backdrop-blur-sm text-gold hover:text-gold-light hover:bg-black/90 p-4 rounded-full transition-all duration-300 hover:scale-110 border border-gold/30 hover:border-gold/50 shadow-lg"
                >
                  {isSlideshow ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                </button>
              </>
            )}

            {/* Enhanced Image Counter */}
            <div className="absolute bottom-6 left-1/2 -translate-x-1/2 bg-black/70 backdrop-blur-lg text-gold px-8 py-4 rounded-full border border-gold/30 shadow-lg">
              <div className="flex items-center gap-4">
                <span className="text-lg font-serif">
                  {selectedImage + 1} / {store.images.length}
                </span>
                {isSlideshow && (
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-gold rounded-full animate-pulse"></div>
                    <span className="text-sm">Auto</span>
                  </div>
                )}
              </div>
            </div>

            {/* Image Thumbnails */}
            <div className="absolute bottom-20 left-1/2 -translate-x-1/2 max-w-full overflow-x-auto">
              <div className="flex gap-2 px-4">
                {store.images.map((img, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={`lightbox-thumbnail relative flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden transition-all duration-300 ${
                      index === selectedImage 
                        ? 'ring-2 ring-gold scale-110 active' 
                        : 'ring-1 ring-gold/30 hover:ring-gold/60 opacity-70 hover:opacity-100'
                    }`}
                  >
                    <img
                      src={img}
                      alt={`Thumbnail ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default StoreGallery;
