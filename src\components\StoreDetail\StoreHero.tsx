import React, { useRef } from "react";
import { useLanguage } from "@/hooks/use-language";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";
import BackgroundSlideshow from "@/components/BackgroundSlideshow";
import { Store } from "@/data/storeData";

interface StoreHeroProps {
  store: Store;
}

const StoreHero: React.FC<StoreHeroProps> = ({ store }) => {
  const { language } = useLanguage();
  const heroRef = useRef<HTMLDivElement>(null);
  const isHeroInView = useIntersectionObserver(heroRef, { threshold: 0.1 });

  return (
    <section 
      ref={heroRef}
      className={`relative h-screen overflow-hidden reveal ${isHeroInView ? 'active' : ''}`}
    >
      <BackgroundSlideshow 
        images={store.images}
        interval={4000}
        className="absolute inset-0"
      />
      
      {/* Store Name Overlay */}
      <div className="absolute inset-0 z-30 flex items-center justify-center">
        <div className="text-center px-4">
          <h1 className="text-3xl md:text-5xl lg:text-7xl font-serif text-gold mb-4 gold-glow drop-shadow-2xl">
            {language === 'en' ? store.nameEn : store.name}
          </h1>
          <div className="w-16 md:w-24 h-1 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto"></div>
        </div>
      </div>
    </section>
  );
};

export default StoreHero;
