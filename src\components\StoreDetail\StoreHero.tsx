import React, { useRef, useState, useEffect, useMemo } from "react";
import { useLanguage } from "@/hooks/use-language";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";
import BackgroundSlideshow from "@/components/BackgroundSlideshow";
import { Store } from "@/data/storeData";

interface StoreHeroProps {
  store: Store;
}

const StoreHero: React.FC<StoreHeroProps> = ({ store }) => {
  const { language } = useLanguage();
  const heroRef = useRef<HTMLDivElement>(null);
  const isHeroInView = useIntersectionObserver(heroRef, { threshold: 0.1 });
  const [isMobile, setIsMobile] = useState(false);

  // Mobile detection with cleanup
  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();

    const mediaQuery = window.matchMedia('(max-width: 767px)');
    const handleChange = (e: MediaQueryListEvent) => setIsMobile(e.matches);

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Memoize store name to prevent unnecessary re-renders
  const storeName = useMemo(() =>
    language === 'en' ? store.nameEn : store.name,
    [language, store.nameEn, store.name]
  );

  // Mobile-optimized styles
  const titleStyles = useMemo(() => ({
    willChange: isMobile ? 'auto' : 'transform',
    transform: isMobile ? 'translateZ(0)' : undefined
  }), [isMobile]);

  return (
    <section
      ref={heroRef}
      className={`relative h-screen overflow-hidden reveal ${isHeroInView ? 'active' : ''}`}
      style={{ willChange: isHeroInView ? 'opacity' : 'auto' }}
    >
      <BackgroundSlideshow
        images={store.images}
        interval={isMobile ? 6000 : 4000} // Slower on mobile
        className="absolute inset-0"
        enableAnimations={!isMobile} // Disable heavy animations on mobile
      />

      {/* Store Name Overlay */}
      <div className="absolute inset-0 z-30 flex items-center justify-center">
        <div className="text-center px-4">
          <h1
            className={`text-3xl md:text-5xl lg:text-7xl font-serif text-gold mb-4 drop-shadow-2xl ${
              isMobile ? '' : 'gold-glow'
            }`}
            style={titleStyles}
          >
            {storeName}
          </h1>
          <div className="w-16 md:w-24 h-1 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto"></div>
        </div>
      </div>
    </section>
  );
};

export default StoreHero;
