import React, { useRef } from "react";
import { useLanguage } from "@/hooks/use-language";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";
import { Button } from "@/components/ui/button";
import { MapPin, Phone, Clock, Navigation, Users, Star } from "lucide-react";
import { Store } from "@/data/storeData";

interface StoreInfoProps {
  store: Store;
}

const StoreInfo: React.FC<StoreInfoProps> = ({ store }) => {
  const { language } = useLanguage();
  const infoRef = useRef<HTMLDivElement>(null);
  const isInfoInView = useIntersectionObserver(infoRef, { threshold: 0.1 });

  const handlePhoneCall = () => {
    window.location.href = `tel:${store.phone}`;
  };

  return (
    <section 
      ref={infoRef}
      className={`py-20 md:py-32 px-4 md:px-8 reveal ${isInfoInView ? 'active' : ''}`}
    >
      <div className="max-w-6xl mx-auto">
        <div className={`grid gap-12 md:gap-16 ${store.id === 1 ? 'lg:grid-cols-2' : 'lg:grid-cols-1'}`}>
          {/* Store Information */}
          <div>
            <h2 className="text-3xl md:text-4xl font-serif text-gold mb-8 md:mb-12 gold-glow">
              {language === 'en' ? 'Store Information' : '店舗情報'}
            </h2>
            
            <div className="space-y-8">
              {/* Address */}
              <div className="group">
                <div className="flex items-start gap-4">
                  <div className="bg-gold/10 p-3 rounded-full group-hover:bg-gold/20 transition-colors duration-300">
                    <MapPin className="w-6 h-6 text-gold" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-gold font-medium mb-2">
                      {language === 'en' ? 'Address' : '住所'}
                    </h3>
                    <p className="text-gold-light leading-relaxed mb-4">
                      {language === 'en' ? store.addressEn : store.address}
                    </p>
                    <Button 
                      onClick={() => {
                        const address = language === 'en' ? store.addressEn : store.address;
                        const googleMapsUrl = `${store.maplink}`;
                        window.open(googleMapsUrl, '_blank');
                      }}
                      variant="ghost" 
                      className="text-gold hover:text-gold-light hover:bg-gold/10 p-0 h-auto font-normal group-hover:translate-x-1 transition-transform duration-300"
                    >
                      <Navigation className="w-4 h-4 mr-2" />
                      {language === 'en' ? 'Get Directions' : 'Google Mapで見る'}
                    </Button>
                  </div>
                </div>
              </div>
              
              {/* Phone */}
              <div className="group">
                <div className="flex items-start gap-4">
                  <div className="bg-gold/10 p-3 rounded-full group-hover:bg-gold/20 transition-colors duration-300">
                    <Phone className="w-6 h-6 text-gold" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-gold font-medium mb-2">
                      {language === 'en' ? 'Phone' : 'お電話'}
                    </h3>
                    <button 
                      onClick={handlePhoneCall}
                      className="text-gold-light hover:text-gold transition-all duration-300 font-medium text-lg group-hover:scale-105"
                    >
                      {store.phone}
                    </button>
                  </div>
                </div>
              </div>

              {/* Hours */}
              <div className="group">
                <div className="flex items-start gap-4">
                  <div className="bg-gold/10 p-3 rounded-full group-hover:bg-gold/20 transition-colors duration-300">
                    <Clock className="w-6 h-6 text-gold" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-gold font-medium mb-2">
                      {language === 'en' ? 'Operating Hours' : '営業時間'}
                    </h3>
                    <p className="text-gold-light text-lg">{store.hours}</p>
                    <p className="text-gold-light/60 text-sm mt-1">
                      {language === 'en' ? 'Daily' : '毎日営業'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Access */}
              <div className="group">
                <div className="flex items-start gap-4">
                  <div className="bg-gold/10 p-3 rounded-full group-hover:bg-gold/20 transition-colors duration-300">
                    <Users className="w-6 h-6 text-gold" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-gold font-medium mb-2">
                      {language === 'en' ? 'Access' : 'アクセス'}
                    </h3>
                    <p className="text-gold-light mb-3">
                      {store.nearestStation}
                    </p>
                    
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Concept - Only show for BRILLAR store */}
          {store.id === 1 && (
            <div>
              <h2 className="text-3xl md:text-4xl font-serif text-gold mb-8 md:mb-12 gold-glow">
                {language === 'en' ? 'Our Concept' : 'コンセプト'}
              </h2>
              
              <div className="bg-gradient-to-br from-black/60 to-gray-900/60 rounded-2xl p-8 border border-gold/20 hover:border-gold/40 transition-all duration-500 backdrop-blur-sm">
                <p className="text-gold-light leading-relaxed text-lg mb-8">
                  {language === 'en' ? store.concept.en : store.concept.ja}
                </p>
                
                

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 mt-8">
                  <Button
                    onClick={handlePhoneCall}
                    className="royal-button text-black font-semibold px-6 py-3 rounded-full hover:scale-105 transition-all duration-300 flex-1"
                  >
                    {language === 'en' ? 'Call for Reservation' : '予約のお電話'}
                  </Button>
                  
                  <Button
                    variant="ghost"
                    className="text-gold hover:text-gold-light hover:bg-gold/10 border border-gold/20 hover:border-gold/40 transition-all duration-300 px-6 py-3 rounded-full flex-1"
                  >
                    {language === 'en' ? 'View Menu' : 'メニューを見る'}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default StoreInfo;
