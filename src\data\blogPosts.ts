
export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  date: string;
  image: string;
  tags: string[];
  readTime: number;
}

export const blogPosts: BlogPost[] = [
  {
    id: "1",
    title: "The Art of Performance: Bringing Characters to Life",
    excerpt: "Explore the techniques and methods that professional actors use to create memorable and authentic characters on stage.",
    content: `
      <p>Performance art is one of the most demanding yet rewarding forms of creative expression. At BRILLAR, we believe that every actor has the potential to create magic on stage, but it requires dedication, technique, and a deep understanding of the craft.</p>
      
      <h2>Understanding Your Character</h2>
      <p>The foundation of any great performance lies in understanding your character's motivations, background, and emotional journey. This process begins long before you step onto the stage.</p>
      
      <h2>Physical Expression</h2>
      <p>Your body is your instrument. Learning to control and express through physical movement is crucial for creating believable characters that resonate with audiences.</p>
      
      <h2>Emotional Authenticity</h2>
      <p>Audiences can sense authenticity. The key is to find genuine emotional connections to your character's experiences, even if they're vastly different from your own.</p>
      
      <p>Remember, great performances aren't just about talent – they're about preparation, practice, and the courage to be vulnerable on stage.</p>
    `,
    author: "<PERSON>",
    date: "2024-01-15",
    image: "/placeholder.svg",
    tags: ["Acting", "Performance", "Technique"],
    readTime: 5
  },
  {
    id: "2",
    title: "Behind the Scenes: Creating Theatrical Magic",
    excerpt: "Discover the intricate work that goes into producing a theatrical performance, from set design to lighting and sound.",
    content: `
      <p>Theater is a collaborative art form that brings together numerous creative disciplines to create a unified experience. Each element plays a crucial role in telling the story.</p>
      
      <h2>Set Design and Visual Storytelling</h2>
      <p>The stage design sets the world of the play. Every prop, color choice, and spatial arrangement contributes to the narrative and helps transport audiences to different times and places.</p>
      
      <h2>Lighting as Emotional Language</h2>
      <p>Lighting design is poetry written in light and shadow. It can shift mood, indicate time passage, and focus audience attention in ways that support the story being told.</p>
      
      <h2>Sound and Music</h2>
      <p>From subtle ambient sounds to powerful musical scores, audio design creates atmosphere and emotional depth that enhances every moment of the performance.</p>
    `,
    author: "Hiroshi Yamamoto",
    date: "2024-01-08",
    image: "/placeholder.svg",
    tags: ["Theater", "Production", "Design"],
    readTime: 4
  },
  {
    id: "3",
    title: "The Future of Theater in Digital Age",
    excerpt: "How modern technology is transforming theatrical experiences while preserving the essence of live performance.",
    content: `
      <p>The digital age has brought both challenges and opportunities to the world of theater. While nothing can replace the magic of live performance, technology offers new ways to enhance and share theatrical experiences.</p>
      
      <h2>Virtual and Augmented Reality</h2>
      <p>Emerging technologies are creating new possibilities for immersive storytelling, allowing audiences to experience theater in completely new ways.</p>
      
      <h2>Streaming and Accessibility</h2>
      <p>Digital platforms have made theater more accessible to global audiences, breaking down geographical barriers and creating new opportunities for artists.</p>
      
      <h2>Preserving Live Performance</h2>
      <p>Despite technological advances, the core of theater remains the live connection between performer and audience – an irreplaceable human experience.</p>
    `,
    author: "Emily Chen",
    date: "2024-01-01",
    image: "/placeholder.svg",
    tags: ["Technology", "Future", "Innovation"],
    readTime: 6
  }
];
