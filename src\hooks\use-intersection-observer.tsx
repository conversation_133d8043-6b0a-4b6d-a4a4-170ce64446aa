
import { useState, useEffect, RefObject } from 'react';

interface IntersectionObserverOptions {
  threshold?: number | number[];
  rootMargin?: string;
  root?: Element | null;
  triggerOnce?: boolean;
}

export const useIntersectionObserver = (
  elementRef: RefObject<Element>,
  options: IntersectionObserverOptions = {}
): boolean => {
  const [isIntersecting, setIsIntersecting] = useState(false);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsIntersecting(true);
          // Optionally stop observing after first intersection
          if (options.triggerOnce) {
            observer.unobserve(element);
          }
        } else if (!options.triggerOnce) {
          setIsIntersecting(false);
        }
      },
      {
        threshold: options.threshold || 0.05, // Lowered from 0 to 0.05 for better responsiveness
        rootMargin: options.rootMargin || '0px 0px 0px 0px', // Trigger immediately when entering viewport
        root: options.root || null,
      }
    );

    observer.observe(element);

    return () => {
      observer.disconnect();
    };
  }, [elementRef, options.threshold, options.rootMargin, options.root, options.triggerOnce]);

  return isIntersecting;
};

// Enhanced hook for smooth progressive animations
export const useSmoothIntersection = (
  elementRef: RefObject<Element>,
  options: IntersectionObserverOptions = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasTriggered) {
          setIsIntersecting(true);
          setHasTriggered(true);
        }
      },
      {
        threshold: options.threshold || 0.02, // Much lower threshold for immediate triggering
        rootMargin: options.rootMargin || '50px 0px 0px 0px', // Start animation before element enters
        root: options.root || null,
      }
    );

    observer.observe(element);

    return () => {
      observer.disconnect();
    };
  }, [elementRef, options.threshold, options.rootMargin, options.root, hasTriggered]);

  return { isIntersecting, hasTriggered };
};
