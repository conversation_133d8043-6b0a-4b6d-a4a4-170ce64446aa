import { useState, useEffect, useCallback } from 'react';

// Mobile-optimized performance hook
export function useMobileOptimized() {
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [isLowEndDevice, setIsLowEndDevice] = useState<boolean>(false);
  const [reducedMotion, setReducedMotion] = useState<boolean>(false);

  useEffect(() => {
    // Check device capabilities
    const checkDeviceCapabilities = () => {
      const width = window.innerWidth;
      const mobile = width < 768;
      setIsMobile(mobile);

      // Detect low-end devices
      const connection = (navigator as any).connection;
      const memory = (navigator as any).deviceMemory;
      const cores = navigator.hardwareConcurrency;
      
      // Low-end device detection
      const isSlowConnection = connection && (connection.effectiveType === '2g' || connection.effectiveType === '3g');
      const isLowMemory = memory && memory <= 4;
      const isLowCores = cores && cores <= 2;
      
      setIsLowEndDevice(mobile && (isSlowConnection || isLowMemory || isLowCores));

      // Check for reduced motion preference
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
      setReducedMotion(prefersReducedMotion || isLowEndDevice);
    };

    checkDeviceCapabilities();
    
    const mediaQuery = window.matchMedia('(max-width: 767px)');
    const motionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    mediaQuery.addEventListener('change', checkDeviceCapabilities);
    motionQuery.addEventListener('change', checkDeviceCapabilities);
    
    return () => {
      mediaQuery.removeEventListener('change', checkDeviceCapabilities);
      motionQuery.removeEventListener('change', checkDeviceCapabilities);
    };
  }, []);

  // Optimized animation variants for mobile
  const getAnimationProps = useCallback((baseProps: any) => {
    if (reducedMotion || isLowEndDevice) {
      return {
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        transition: { duration: 0.1 }
      };
    }
    return baseProps;
  }, [reducedMotion, isLowEndDevice]);

  // Throttled scroll handler
  const createThrottledHandler = useCallback((handler: Function, delay = 16) => {
    let ticking = false;
    return (...args: any[]) => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handler(...args);
          ticking = false;
        });
        ticking = true;
      }
    };
  }, []);

  return {
    isMobile,
    isLowEndDevice,
    reducedMotion,
    getAnimationProps,
    createThrottledHandler,
    // Performance CSS classes
    getPerformanceClasses: useCallback((baseClasses: string) => {
      if (isMobile) {
        return baseClasses
          .replace(/backdrop-blur-[a-z0-9]+/g, '') // Remove backdrop blur on mobile
          .replace(/hover:scale-\[[\d.]+\]/g, '') // Remove scale animations
          .replace(/transition-all/g, 'transition-opacity') // Simplify transitions
          .replace(/duration-[0-9]+/g, 'duration-150'); // Faster transitions
      }
      return baseClasses;
    }, [isMobile])
  };
}

// Optimized intersection observer hook
export function useOptimizedIntersection(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const { reducedMotion } = useMobileOptimized();

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // Use larger threshold for mobile to reduce callback frequency
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      {
        threshold: options.threshold || (window.innerWidth < 768 ? 0.1 : 0.05),
        rootMargin: options.rootMargin || '50px',
        ...options
      }
    );

    observer.observe(element);
    return () => observer.disconnect();
  }, [elementRef, options.threshold, options.rootMargin, reducedMotion]);

  return isIntersecting;
}
