@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cormorant:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lora:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Serif+JP:wght@300;400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;700&display=swap');
/* Removed problematic edwardian-script-itc font causing 500 error */

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 3%; /* Deeper black for royal elegance */
    --foreground: 45 57% 52%;

    --card: 0 0% 8%; /* Darker cards for luxury feel */
    --card-foreground: 45 57% 52%;

    --popover: 0 0% 8%;
    --popover-foreground: 45 57% 52%;

    --primary: 45 57% 52%;
    --primary-foreground: 0 0% 3%;

    --secondary: 0 0% 12%; /* Darker secondary */
    --secondary-foreground: 45 57% 52%;

    --muted: 0 0% 12%;
    --muted-foreground: 45 40% 40%;

    --accent: 45 57% 52%;
    --accent-foreground: 0 0% 3%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 45 57% 52%;

    --border: 45 40% 25%; /* More subtle borders */
    --input: 45 40% 25%;
    --ring: 45 57% 52%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
    margin: 0;
    padding: 0;
  }

  html {
    scroll-behavior: smooth;
    @apply bg-background text-foreground;
    overflow-x: hidden; /* Prevent horizontal scroll */
    margin: 0;
    padding: 0;
  }

  body {
    @apply bg-gradient-to-br from-black via-gray-900 to-black text-foreground font-sans-jp;
    background-attachment: fixed;
    overflow-x: hidden; /* Prevent horizontal scroll */
    width: 100%;
    max-width: 100vw; /* Ensure body doesn't exceed viewport width */
    margin: 0;
    padding: 0;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-serif text-gold;
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
  }

  section {
    @apply py-20 px-4 md:px-8 relative;
    background: linear-gradient(135deg, rgba(0,0,0,0.9) 0%, rgba(20,20,20,0.95) 50%, rgba(0,0,0,0.9) 100%);
    width: 100%;
    max-width: 100vw; /* Ensure sections don't exceed viewport width */
    box-sizing: border-box; /* Include padding in width calculation */
  }

  /* Override for hero section to remove padding */
  section#hero {
    padding: 0 !important;
    margin: 0 !important;
  }

  section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(212, 175, 55, 0.02) 0%, transparent 70%);
    pointer-events: none;
  }

  /* Ensure all containers are responsive */
  .container {
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
  }

  /* Smooth scrolling only for anchor links and internal navigation */
  html:has(a[href^="#"]:active) {
    scroll-behavior: smooth;
  }
  
  /* Allow manual override for instant scrolling */
  html.scroll-instant {
    scroll-behavior: auto !important;
  }
}

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Responsive utility classes */
  .responsive-container {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
    box-sizing: border-box;
  }
}

@keyframes zoom-slow {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.05);
  }
}

@keyframes royal-shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}

.animate-zoom-slow {
  animation: zoom-slow 15s ease-in-out infinite alternate;
}

.animate-royal-shimmer {
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.4), transparent);
  background-size: 200% 100%;
  animation: royal-shimmer 3s infinite;
}

/* Mobile optimization: Disable heavy animations on mobile */
@media (max-width: 768px) {
  .animate-zoom-slow {
    animation: none;
    transform: scale(1);
  }

  .animate-royal-shimmer {
    animation: none;
    background: rgba(212, 175, 55, 0.2);
  }
}

.section-heading {
  @apply text-4xl md:text-5xl font-serif font-bold mb-12 text-center text-gold relative;
  background: linear-gradient(45deg, #D4AF37, #FFD700, #D4AF37);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 30px rgba(212, 175, 55, 0.5);
}

.section-heading::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #D4AF37, transparent);
}

.reveal {
  @apply opacity-0;
}

.reveal.active {
  @apply animate-fade-in;
}

.story-link {
  @apply relative inline-block after:content-[''] after:absolute after:w-full after:scale-x-0 after:h-0.5 after:bottom-0 after:left-0 after:bg-primary after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left;
}

.hover-scale {
  @apply transition-all duration-300 hover:scale-105;
  filter: drop-shadow(0 0 15px rgba(212, 175, 55, 0.2));
}

.hover-scale:hover {
  filter: drop-shadow(0 0 25px rgba(212, 175, 55, 0.4));
}

.nav-link {
  @apply relative text-gold transition-all duration-300 hover:text-gold-light;
  text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
}

.nav-link::after {
  @apply content-[''] absolute w-0 h-[2px] bg-gradient-to-r from-transparent via-gold to-transparent left-1/2 -translate-x-1/2 bottom-0 transition-all duration-300;
}

.nav-link:hover::after {
  @apply w-full;
}

.cast-card {
  @apply bg-card rounded-lg overflow-hidden transition-all duration-500 hover:scale-[1.03];
  background: linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(20,20,20,0.9) 100%);
  border: 1px solid rgba(212, 175, 55, 0.2);
  box-shadow: 0 10px 30px rgba(0,0,0,0.5);
}

.cast-card:hover {
  box-shadow: 0 20px 50px rgba(212, 175, 55, 0.2), 0 0 30px rgba(212, 175, 55, 0.1);
  border-color: rgba(212, 175, 55, 0.4);
}

.cast-card img {
  @apply w-full h-48 object-cover object-top transition-transform duration-500;
}

.cast-card:hover img {
  transform: scale(1.1);
}

/* Mobile optimization for cast cards */
@media (max-width: 768px) {
  .cast-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .cast-card:hover {
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.15);
    border-color: rgba(212, 175, 55, 0.3);
  }

  .cast-card img {
    transition: transform 0.2s ease;
  }

  .cast-card:hover img {
    transform: scale(1.05);
  }
}

/* Enhanced Gold glow for luxury elements */
.gold-glow {
  text-shadow: 0 0 20px rgba(212, 175, 55, 0.6), 0 0 40px rgba(212, 175, 55, 0.3);
  filter: drop-shadow(0 0 10px rgba(212, 175, 55, 0.4));
}

/* Mobile optimization: Disable heavy glow effects on mobile */
@media (max-width: 768px) {
  .gold-glow {
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.4);
    filter: none;
  }
}

/* Custom dropdown menu for royal elegance */
.custom-dropdown {
  @apply bg-black/80 backdrop-blur-sm border border-gold/30 hover:border-gold/60 transition-all duration-300;
  box-shadow: 0 10px 30px rgba(0,0,0,0.5), 0 0 20px rgba(212, 175, 55, 0.1);
}

/* Royal button styling */
.royal-button {
  background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #D4AF37 100%);
  border: 2px solid transparent;
  background-clip: padding-box;
  position: relative;
  overflow: hidden;
}

.royal-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.royal-button:hover::before {
  left: 100%;
}

/* Elegant custom scrollbar with gold theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0,0,0,0.5);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #D4AF37, #FFD700);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #FFD700, #D4AF37);
}

/* Performance optimizations to prevent shakiness */
*, *::before, *::after {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Optimize transforms */
.optimize-transforms {
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Remove will-change after transitions complete */
.optimize-transforms:not(:hover):not(:focus):not(:active) {
  will-change: auto;
}

/* Custom styles for engraved wood texture and Japanese yakuza aesthetics */
.shadow-text {
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8), 0 0 4px rgba(0, 0, 0, 0.3);
}

.shadow-inner-2xl {
  box-shadow: inset 0 25px 50px -12px rgba(0, 0, 0, 0.8);
}

.wood-grain {
  background-image: 
    linear-gradient(90deg, rgba(139, 69, 19, 0.1) 50%, transparent 50%),
    linear-gradient(rgba(160, 82, 45, 0.05) 50%, transparent 50%);
  background-size: 4px 4px, 2px 2px;
}

.engraved-text {
  background: linear-gradient(145deg, #8b4513, #654321);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), inset 0 1px 2px rgba(255, 255, 255, 0.1);
}

/* Japanese traditional patterns */
.seigaiha-pattern {
  background-image: radial-gradient(circle at 100% 50%, transparent 20%, rgba(212, 175, 55, 0.1) 21%, rgba(212, 175, 55, 0.1) 34%, transparent 35%, transparent), linear-gradient(0deg, transparent 24%, rgba(212, 175, 55, 0.05) 25%, rgba(212, 175, 55, 0.05) 26%, transparent 27%, transparent 74%, rgba(212, 175, 55, 0.05) 75%, rgba(212, 175, 55, 0.05) 76%, transparent 77%, transparent);
  background-size: 40px 40px;
}

/* Enhanced button styles */
.yakuza-button {
  position: relative;
  background: linear-gradient(145deg, #8b4513, #654321, #3e2723);
  border: 2px solid rgba(0, 0, 0, 0.4);
  box-shadow: 
    inset 0 2px 4px rgba(255, 255, 255, 0.1),
    inset 0 -2px 4px rgba(0, 0, 0, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.4);
}

.yakuza-button::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.1) 50%, transparent 70%);
  pointer-events: none;
}

/* Form input engraved effect */
.engraved-input {
  background: rgba(245, 245, 220, 0.1);
  border: 2px solid rgba(139, 69, 19, 0.6);
  box-shadow: 
    inset 0 2px 8px rgba(0, 0, 0, 0.4),
    inset 0 -1px 2px rgba(255, 255, 255, 0.1);
}

.engraved-input:focus {
  border-color: rgba(0, 0, 0, 0.8);
  box-shadow: 
    inset 0 2px 8px rgba(0, 0, 0, 0.5),
    inset 0 -1px 2px rgba(255, 255, 255, 0.1),
    0 0 0 1px rgba(139, 69, 19, 0.3);
}

/* Enhanced Gallery Luxury Effects */
@keyframes luxuryFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-2px) rotate(0.5deg); }
}

@keyframes goldShimmer {
  0% { background-position: -200% center; }
  100% { background-position: 200% center; }
}

@keyframes diamondSparkle {
  0%, 100% { opacity: 0; transform: scale(0.5) rotate(0deg); }
  50% { opacity: 1; transform: scale(1) rotate(180deg); }
}

@keyframes luxuryPulse {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
    transform: scale(1);
  }
  50% { 
    box-shadow: 0 0 40px rgba(212, 175, 55, 0.6);
    transform: scale(1.02);
  }
}

@keyframes premiumGlow {
  0%, 100% { filter: drop-shadow(0 0 10px rgba(212, 175, 55, 0.3)); }
  50% { filter: drop-shadow(0 0 25px rgba(212, 175, 55, 0.7)); }
}

@keyframes elegantScale {
  0% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.05) rotate(1deg); }
  100% { transform: scale(1.1) rotate(0deg); }
}

/* Gallery specific styles */
.gallery-card {
  position: relative;
  overflow: hidden;
  border-radius: 24px;
  background: linear-gradient(145deg, rgba(0,0,0,0.9), rgba(20,20,20,0.95));
  box-shadow: 
    0 10px 30px rgba(0,0,0,0.5),
    inset 0 1px 2px rgba(212, 175, 55, 0.1);
  transition: all 0.7s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.gallery-card:hover {
  animation: luxuryPulse 2s ease-in-out infinite;
  box-shadow: 
    0 20px 60px rgba(0,0,0,0.7),
    0 0 40px rgba(212, 175, 55, 0.4),
    inset 0 1px 2px rgba(212, 175, 55, 0.2);
}

.gallery-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(212, 175, 55, 0.2),
    transparent
  );
  transition: left 0.8s;
  z-index: 1;
}

.gallery-card:hover::before {
  left: 100%;
}

.gallery-image {
  transition: all 0.7s cubic-bezier(0.25, 0.8, 0.25, 1);
  filter: brightness(0.9) contrast(1.1);
}

.gallery-card:hover .gallery-image {
  transform: scale(1.1) rotate(1deg);
  filter: brightness(1.1) contrast(1.2) saturate(1.1);
}

.luxury-border {
  position: relative;
  border: 2px solid transparent;
  background: linear-gradient(black, black) padding-box,
              linear-gradient(45deg, #d4af37, #f4e4bc, #d4af37) border-box;
}

.diamond-sparkle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #d4af37;
  border-radius: 50%;
  animation: diamondSparkle 3s ease-in-out infinite;
}

.diamond-sparkle:nth-child(1) { top: 10%; left: 20%; animation-delay: 0s; }
.diamond-sparkle:nth-child(2) { top: 20%; right: 15%; animation-delay: 0.5s; }
.diamond-sparkle:nth-child(3) { bottom: 30%; left: 25%; animation-delay: 1s; }
.diamond-sparkle:nth-child(4) { bottom: 15%; right: 20%; animation-delay: 1.5s; }

.premium-gradient-text {
  background: linear-gradient(135deg, #d4af37 0%, #f4e4bc 25%, #d4af37 50%, #b8860b 75%, #d4af37 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 300% 300%;
  animation: goldShimmer 3s ease-in-out infinite;
}

.lightbox-backdrop {
  backdrop-filter: blur(20px) saturate(180%);
  background: rgba(0, 0, 0, 0.95);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.lightbox-image {
  animation: lightboxZoom 0.4s ease-out;
  filter: drop-shadow(0 0 50px rgba(212, 175, 55, 0.3));
}

@keyframes lightboxZoom {
  from { 
    opacity: 0; 
    transform: scale(0.8) rotate(-2deg); 
  }
  to { 
    opacity: 1; 
    transform: scale(1) rotate(0deg); 
  }
}

.gallery-filter-btn {
  position: relative;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(212, 175, 55, 0.3);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.gallery-filter-btn:hover {
  background: rgba(212, 175, 55, 0.2);
  border-color: rgba(212, 175, 55, 0.6);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
}

.gallery-filter-btn.active {
  background: rgba(212, 175, 55, 0.3);
  border-color: rgba(212, 175, 55, 0.8);
  box-shadow: 0 0 20px rgba(212, 175, 55, 0.4);
}

/* Thumbnail navigation styles */
.lightbox-thumbnail {
  transition: all 0.3s ease;
  filter: brightness(0.7) contrast(1.2);
}

.lightbox-thumbnail:hover {
  filter: brightness(1) contrast(1.3);
  transform: scale(1.1);
}

.lightbox-thumbnail.active {
  filter: brightness(1.2) contrast(1.4);
  transform: scale(1.15);
  box-shadow: 0 0 15px rgba(212, 175, 55, 0.6);
}

/* Stats section enhancements */
.gallery-stats {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(212, 175, 55, 0.3);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.5),
    inset 0 1px 2px rgba(212, 175, 55, 0.1);
}

.gallery-stats:hover {
  border-color: rgba(212, 175, 55, 0.5);
  box-shadow: 
    0 25px 80px rgba(0, 0, 0, 0.6),
    0 0 40px rgba(212, 175, 55, 0.3),
    inset 0 1px 2px rgba(212, 175, 55, 0.2);
}

.stat-number {
  transition: all 0.3s ease;
}

.stat-number:hover {
  animation: premiumGlow 2s ease-in-out infinite;
  transform: scale(1.1);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .gallery-card {
    border-radius: 16px;
    transition: transform 0.2s ease;
  }

  .gallery-card:hover {
    animation: none;
    transform: scale(1.02);
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
  }

  .gallery-card::before {
    display: none; /* Disable shimmer effect on mobile */
  }

  .gallery-image {
    transition: transform 0.2s ease;
  }

  .gallery-card:hover .gallery-image {
    transform: scale(1.05);
    filter: brightness(1.05);
  }

  .lightbox-image {
    animation: none;
  }

  /* Disable heavy hover effects on mobile */
  .hover-scale {
    transition: transform 0.2s ease;
  }

  .hover-scale:hover {
    transform: scale(1.02);
    filter: none;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .gallery-card,
  .gallery-image,
  .stat-number {
    animation: none !important;
    transition: none !important;
  }
  
  .gallery-card:hover {
    transform: none !important;
  }
}
