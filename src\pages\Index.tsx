import React, { useState, useEffect, Suspense, lazy } from "react";
import Hero from "@/components/Hero";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Preloader from "@/components/Preloader";

// Lazy load heavy components
const About = lazy(() => import("@/components/About"));
const Gallery = lazy(() => import("@/components/Gallery"));
const NoticeBoard = lazy(() => import("@/components/NoticeBoard"));
const EventsMemories = lazy(() => import("@/components/EventsMemories"));
const SocialMedia = lazy(() => import("@/components/SocialMedia"));
const StoreLocations = lazy(() => import("@/components/StoreLocations"));
const ShopOverview = lazy(() => import("@/components/ShopOverview"));
const RecruitmentBanner = lazy(() => import("@/components/RecruitmentBanner"));
const ContactUs = lazy(() => import("@/components/ui/ContactUs"));

// Loading component for lazy-loaded sections
const SectionLoader = () => (
  <div className="py-20 flex justify-center">
    <div className="w-6 h-6 border-2 border-gold border-t-transparent rounded-full animate-spin"></div>
  </div>
);


const Index = () => {
  // Check if this is initial load or navigation from other pages
  const [isLoading, setIsLoading] = useState(() => {
    // Check if this is marked as internal navigation
    const isInternalNav = sessionStorage.getItem('brillar-internal-nav');
    
    // Clear the internal navigation flag after checking
    if (isInternalNav) {
      sessionStorage.removeItem('brillar-internal-nav');
      console.log('🔄 Internal navigation detected - skipping preloader');
      return false; // Don't show preloader for internal navigation
    }
    
    // Check if user has visited during this session
    const hasVisited = sessionStorage.getItem('brillar-visited');
    
    // Show preloader only if user hasn't visited this session
    // This covers initial website entry and hard refresh
    const shouldShowPreloader = !hasVisited;
    console.log('🎬 Preloader decision:', {
      hasVisited: !!hasVisited,
      shouldShowPreloader,
      sessionStorage: {
        visited: hasVisited,
        internalNav: isInternalNav
      }
    });
    
    return shouldShowPreloader;
  });

  // Refs for scrolling
  const recruitmentRef = React.useRef<HTMLElement>(null);
  const noticeRef = React.useRef<HTMLElement>(null);

  // Scroll to top on page load/refresh
  useEffect(() => {
    if ('scrollRestoration' in history) {
      history.scrollRestoration = 'manual';
    }
    
    // Only scroll to top if showing preloader (initial visit)
    if (isLoading) {
      window.scrollTo(0, 0);
      const scrollToTop = () => {
        window.scrollTo({
          top: 0,
          left: 0,
          behavior: 'instant'
        });
      };
      scrollToTop();
      const timeoutId = setTimeout(scrollToTop, 100);
      return () => {
        clearTimeout(timeoutId);
      };
    } else {
      // For internal navigation, ensure we start at the top unless there's a specific target
      const returnTarget = sessionStorage.getItem('brillar-return-target');
      if (!returnTarget) {
        window.scrollTo(0, 0);
      }
    }
  }, [isLoading]);

  const handlePreloaderComplete = () => {
    console.log('✅ Preloader completed - marking as visited');
    setIsLoading(false);
    // Mark that user has visited the site during this session
    sessionStorage.setItem('brillar-visited', 'true');
    // Ensure we're at the top after preloader completes
    setTimeout(() => {
      window.scrollTo(0, 0);
    }, 50);
  };

  // Listen for custom event from Navbar for recruitment/notice scroll
  useEffect(() => {
    const handler = (e: any) => {
      const section = e.detail?.section;
      if (section === "recruitment" && recruitmentRef.current) {
        recruitmentRef.current.scrollIntoView({ behavior: "smooth" });
      } else if (section === "notice" && noticeRef.current) {
        noticeRef.current.scrollIntoView({ behavior: "smooth" });
      }
    };
    window.addEventListener("menu-section-scroll", handler);
    return () => window.removeEventListener("menu-section-scroll", handler);
  }, []);

  // Handle navigation from other pages within the site
  useEffect(() => {
    // If not showing preloader (navigating from other pages), 
    // still mark as visited and ensure proper state
    if (!isLoading) {
      sessionStorage.setItem('brillar-visited', 'true');
    }
  }, [isLoading]);

  // Handle intelligent return navigation (scroll to specific sections)
  useEffect(() => {
    // Only execute if not showing preloader (internal navigation)
    if (!isLoading) {
      const returnTarget = sessionStorage.getItem('brillar-return-target');
      if (returnTarget) {
        sessionStorage.removeItem('brillar-return-target');
        // Use a longer timeout to ensure all components are loaded
        setTimeout(() => {
          const targetElement = document.getElementById(returnTarget);
          if (targetElement) {
            targetElement.scrollIntoView({ behavior: 'smooth' });
          }
        }, 500); // Increased timeout for component loading
      }
    }
  }, [isLoading]);

  // Debug effect to track state changes
  useEffect(() => {
    console.log('🏠 Index component mounted with isLoading:', isLoading);
  }, []);

  useEffect(() => {
    console.log('🔄 isLoading state changed:', isLoading);
  }, [isLoading]);

  // Temporary debugging function (remove in production)
  useEffect(() => {
    // Add global function for testing
    (window as any).clearBrillarSession = () => {
      sessionStorage.removeItem('brillar-visited');
      sessionStorage.removeItem('brillar-internal-nav');
      console.log('🧹 Session storage cleared - refresh to see preloader');
    };
    
    // Add global function to check session state
    (window as any).checkBrillarSession = () => {
      console.log('📊 Session state:', {
        visited: sessionStorage.getItem('brillar-visited'),
        internalNav: sessionStorage.getItem('brillar-internal-nav'),
        isLoading: isLoading
      });
    };
  }, [isLoading]);

  return (
    <div className="min-h-screen bg-black w-full overflow-x-hidden">
      {isLoading ? (
        <Preloader onComplete={handlePreloaderComplete} />
      ) : null}
      
      {/* Navbar - Always rendered as overlay */}
      <div className={`${isLoading ? "opacity-0" : "opacity-100 transition-opacity duration-1000"}`}>
        <Navbar />
      </div>
      
      {/* Main content */}
      <div className={`w-full ${isLoading ? "opacity-0" : "opacity-100 transition-opacity duration-1000"}`}>
        <Hero />
        <Suspense fallback={<SectionLoader />}>
          <ShopOverview/>
        </Suspense>
        
        {/* RecruitmentBanner with ref */}
        <section ref={recruitmentRef}>
          <Suspense fallback={<SectionLoader />}>
            <RecruitmentBanner />
          </Suspense>
        </section>
        <Suspense fallback={<SectionLoader />}>
          <Gallery />
        </Suspense>
        {/* NoticeBoard with ref */}
        <section ref={noticeRef}>
          <Suspense fallback={<SectionLoader />}>
            <NoticeBoard />
          </Suspense>
        </section>
        <Suspense fallback={<SectionLoader />}>
          <EventsMemories />
        </Suspense>
        <Suspense fallback={<SectionLoader />}>
          <SocialMedia />
        </Suspense>
        <Suspense fallback={<SectionLoader />}>
          <StoreLocations />
        </Suspense>
        <Suspense fallback={<SectionLoader />}>
          <ContactUs />
        </Suspense>
        <Footer />
      </div>
    </div>
  );
};

export default Index;
