/* Mobile Performance Optimizations */

/* GPU-accelerated transforms */
.mobile-optimized {
  will-change: auto;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Disable heavy effects on mobile */
@media (max-width: 768px) {
  /* Remove backdrop blur on mobile - major performance killer */
  .backdrop-blur-sm,
  .backdrop-blur-md,
  .backdrop-blur-lg,
  .backdrop-blur-xl {
    backdrop-filter: none !important;
    background: rgba(0, 0, 0, 0.8) !important;
  }

  /* Simplify gold glow effect */
  .gold-glow {
    text-shadow: 0 0 5px rgba(212, 175, 55, 0.3) !important;
    filter: none !important;
  }

  /* Reduce box shadow complexity */
  .shadow-lg,
  .shadow-xl,
  .shadow-2xl {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  }

  /* Disable hover effects that cause repaints */
  .hover\\:scale-\\[1\\.02\\]:hover,
  .hover\\:scale-\\[1\\.05\\]:hover,
  .hover\\:scale-110:hover,
  .group:hover .group-hover\\:scale-110 {
    transform: none !important;
  }

  /* Optimize card transitions */
  .gallery-card,
  .cast-card {
    transition: opacity 0.2s ease !important;
    will-change: auto;
  }

  .gallery-card:hover,
  .cast-card:hover {
    transform: none !important;
    animation: none !important;
  }

  /* Disable complex gradient animations */
  .animate-royal-shimmer,
  .animate-zoom-slow {
    animation: none !important;
  }

  /* Simplify button hover effects */
  .royal-button:hover {
    transform: none !important;
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.2) !important;
  }

  /* Remove expensive filter effects */
  .group-hover\\:brightness-110:hover,
  .hover\\:brightness-110:hover {
    filter: none !important;
  }

  /* Optimize image transforms */
  .group:hover img,
  .hover\\:scale-110:hover img {
    transform: none !important;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Performance-optimized card styles */
.perf-card {
  contain: layout style paint;
  will-change: auto;
  transition: opacity 0.2s ease;
  backface-visibility: hidden;
  transform: translateZ(0);
}

.perf-card:hover {
  opacity: 0.9;
}

/* Mobile-optimized gradients */
@media (max-width: 768px) {
  .bg-gradient-to-br,
  .bg-gradient-to-r,
  .bg-gradient-to-t {
    background: rgba(0, 0, 0, 0.9) !important;
  }
}

/* Optimized scroll performance */
.scroll-container {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  will-change: scroll-position;
}

/* Prevent layout thrashing */
.prevent-layout-shift {
  contain: layout style;
  transform: translateZ(0);
}

/* Mobile-specific image optimization */
@media (max-width: 768px) {
  img {
    will-change: auto;
    backface-visibility: hidden;
    transform: translateZ(0);
  }

  /* Disable image hover effects */
  .group:hover img {
    transform: none !important;
    filter: none !important;
  }
}

/* Battery-efficient animations */
.battery-efficient {
  animation-play-state: paused;
}

@media (max-width: 768px) and (prefers-reduced-motion: no-preference) {
  .battery-efficient {
    animation-play-state: running;
    animation-duration: 2s;
    animation-timing-function: ease-out;
  }
}

/* Optimize font rendering */
@media (max-width: 768px) {
  * {
    text-rendering: optimizeSpeed;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}
